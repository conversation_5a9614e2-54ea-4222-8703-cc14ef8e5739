import { Injectable, NotFoundException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaginateConfig, PaginateQuery, paginate } from 'nestjs-paginate';
import { PurchaseOrder, PurchaseOrderStatus } from './entities/purchase-order.entity';
import { PurchaseOrderItem } from './entities/purchase-order-item.entity';
import { CreatePurchaseOrderDto } from './dto/create-purchase-order.dto';
import { UpdatePurchaseOrderDto } from './dto/update-purchase-order.dto';
import { InventoryService } from '../inventory/inventory.service';
import { TransactionType } from '../inventory/entities/inventory-transaction.entity';

export const PURCHASE_ORDER_PAGINATION_CONFIG: PaginateConfig<PurchaseOrder> = {
  sortableColumns: ['id', 'poNumber', 'poDate', 'status', 'totalAmount', 'createdAt'],
  nullSort: 'last',
  defaultSortBy: [['createdAt', 'DESC']],
  searchableColumns: ['poNumber'],
  select: [
    'id',
    'poNumber',
    'poDate',
    'expectedDeliveryDate',
    'actualDeliveryDate',
    'status',
    'totalAmount',
    'createdAt',
    'updatedAt',
    'createdBy.id',
    'createdBy.firstName',
    'createdBy.lastName',
    'approvedBy.id',
    'approvedBy.firstName',
    'approvedBy.lastName',
    'branch.id',
    'branch.name',
    'supplier.id',
    'supplier.code',
    'supplier.name',
    'supplier.contactPerson',
    'supplier.phone'
  ],
  relations: {
    createdBy: true,
    approvedBy: true,
    branch: true,
    supplier: true
  },
  filterableColumns: {
    status: true,
    'branch.id': true,
    'createdBy.id': true
  },
  defaultLimit: 20,
  maxLimit: 100,
};

@Injectable()
export class PurchaseOrderService {
  constructor(
    @InjectRepository(PurchaseOrder)
    private readonly purchaseOrderRepository: Repository<PurchaseOrder>,
    @InjectRepository(PurchaseOrderItem)
    private readonly purchaseOrderItemRepository: Repository<PurchaseOrderItem>,
    @Inject(forwardRef(() => InventoryService))
    private readonly inventoryService: InventoryService,
  ) {}

  async datatables(query: PaginateQuery) {
    return paginate(query, this.purchaseOrderRepository, PURCHASE_ORDER_PAGINATION_CONFIG);
  }

  async findAll() {
    return this.purchaseOrderRepository.find({
      relations: {
        items: {
          product: true
        },
        createdBy: true,
        approvedBy: true,
        branch: true,
        supplier: true
      },
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async findOne(id: number) {
    const purchaseOrder = await this.purchaseOrderRepository.findOne({
      where: { id },
      relations: {
        items: {
          product: {
            category: true,
            unit: true
          }
        },
        createdBy: true,
        approvedBy: true,
        branch: true,
        supplier: true
      }
    });
    if (!purchaseOrder) {
      throw new NotFoundException(`Purchase Order with ID ${id} not found`);
    }
    return purchaseOrder;
  }

  async findByPoNumber(poNumber: string) {
    const purchaseOrder = await this.purchaseOrderRepository.findOne({
      where: { poNumber },
      relations: {
        items: {
          product: true
        },
        createdBy: true,
        approvedBy: true,
        branch: true,
        supplier: true
      }
    });
    if (!purchaseOrder) {
      throw new NotFoundException(`Purchase Order with PO Number ${poNumber} not found`);
    }
    return purchaseOrder;
  }

  async create(createPurchaseOrderDto: CreatePurchaseOrderDto, userId: number) {
    // คำนวณยอดรวมจาก items
    const calculatedSubtotal = createPurchaseOrderDto.items.reduce((sum, item) => sum + item.totalPrice, 0);
    const calculatedTaxAmount = calculatedSubtotal * (createPurchaseOrderDto.taxRate || 0) / 100;
    const calculatedTotal = calculatedSubtotal + calculatedTaxAmount - (createPurchaseOrderDto.discountAmount || 0);

    const purchaseOrder = this.purchaseOrderRepository.create({
      poNumber: await this.generatePoNumber(createPurchaseOrderDto.branchId),
      poDate: new Date(createPurchaseOrderDto.poDate),
      expectedDeliveryDate: createPurchaseOrderDto.expectedDeliveryDate ? new Date(createPurchaseOrderDto.expectedDeliveryDate) : null,
      status: createPurchaseOrderDto.status || PurchaseOrderStatus.DRAFT,
      subtotal: calculatedSubtotal,
      taxRate: createPurchaseOrderDto.taxRate || 0,
      taxAmount: calculatedTaxAmount,
      discountAmount: createPurchaseOrderDto.discountAmount || 0,
      totalAmount: calculatedTotal,
      notes: createPurchaseOrderDto.notes,
      createdBy: {
        id: userId
      } as any,
      branch: {
        id: createPurchaseOrderDto.branchId
      } as any,
      supplier: {
        id: createPurchaseOrderDto.supplierId
      } as any,
      items: createPurchaseOrderDto.items.map(item => ({
        productId: item.productId,
        productName: item.productName,
        productCode: item.productCode,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
        receivedQuantity: 0,
        remainingQuantity: item.quantity,
        notes: item.notes,
        product: {
          id: item.productId
        } as any
      }))
    });

    return await this.purchaseOrderRepository.save(purchaseOrder);
  }

  async update(id: number, updatePurchaseOrderDto: UpdatePurchaseOrderDto) {
    const existingPo = await this.findOne(id);

    // ตรวจสอบว่าสามารถแก้ไขได้หรือไม่
    if (existingPo.status === PurchaseOrderStatus.COMPLETED || existingPo.status === PurchaseOrderStatus.CANCELLED) {
      throw new BadRequestException(`Cannot update Purchase Order with status ${existingPo.status}`);
    }

    const updateData: any = { ...updatePurchaseOrderDto };

    if (updatePurchaseOrderDto.poDate) {
      updateData.poDate = new Date(updatePurchaseOrderDto.poDate);
    }
    if (updatePurchaseOrderDto.expectedDeliveryDate) {
      updateData.expectedDeliveryDate = new Date(updatePurchaseOrderDto.expectedDeliveryDate);
    }
    if (updatePurchaseOrderDto.actualDeliveryDate) {
      updateData.actualDeliveryDate = new Date(updatePurchaseOrderDto.actualDeliveryDate);
    }
    if (updatePurchaseOrderDto.approvedAt) {
      updateData.approvedAt = new Date(updatePurchaseOrderDto.approvedAt);
    }
    if (updatePurchaseOrderDto.approvedBy) {
      updateData.approvedBy = { id: updatePurchaseOrderDto.approvedBy };
    }

    await this.purchaseOrderRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: number) {
    const existingPo = await this.findOne(id);

    // ตรวจสอบว่าสามารถลบได้หรือไม่
    if (existingPo.status !== PurchaseOrderStatus.DRAFT) {
      throw new BadRequestException(`Cannot delete Purchase Order with status ${existingPo.status}`);
    }

    return await this.purchaseOrderRepository.softDelete(id);
  }

  async approve(id: number, userId: number) {
    const existingPo = await this.findOne(id);

    if (existingPo.status !== PurchaseOrderStatus.PENDING) {
      throw new BadRequestException(`Cannot approve Purchase Order with status ${existingPo.status}`);
    }

    await this.purchaseOrderRepository.update(id, {
      status: PurchaseOrderStatus.APPROVED,
      approvedBy: { id: userId } as any,
      approvedAt: new Date()
    });

    return this.findOne(id);
  }

  async changeStatus(id: number, status: PurchaseOrderStatus) {
    const existingPo = await this.findOne(id);

    // ตรวจสอบการเปลี่ยนสถานะที่ถูกต้อง
    const validTransitions = this.getValidStatusTransitions(existingPo.status);
    if (!validTransitions.includes(status)) {
      throw new BadRequestException(`Cannot change status from ${existingPo.status} to ${status}`);
    }

    await this.purchaseOrderRepository.update(id, { status });
    return this.findOne(id);
  }

  async receiveItems(id: number, receivedItems: { itemId: number; receivedQuantity: number; warehouseId?: number }[], userId: number) {
    const purchaseOrder = await this.findOne(id);

    if (purchaseOrder.status !== PurchaseOrderStatus.ORDERED) {
      throw new BadRequestException(`Cannot receive items for Purchase Order with status ${purchaseOrder.status}`);
    }

    for (const receivedItem of receivedItems) {
      const item = purchaseOrder.items.find((i: any) => i.id === receivedItem.itemId);
      if (!item) {
        throw new NotFoundException(`Purchase Order Item with ID ${receivedItem.itemId} not found`);
      }

      const newReceivedQuantity = item.receivedQuantity + receivedItem.receivedQuantity;
      if (newReceivedQuantity > item.quantity) {
        throw new BadRequestException(`Received quantity cannot exceed ordered quantity for item ${item.productName}`);
      }

      // อัปเดต PO Item
      await this.purchaseOrderItemRepository.update(receivedItem.itemId, {
        receivedQuantity: newReceivedQuantity,
        remainingQuantity: item.quantity - newReceivedQuantity
      });

      // เพิ่มสินค้าเข้า Inventory
      if (receivedItem.receivedQuantity > 0) {
        try {
          // หา inventory ที่มีอยู่แล้ว หรือสร้างใหม่
          let inventory: any;
          try {
            inventory = await this.inventoryService.findByProductAndLocation(
              item.productId,
              purchaseOrder.branch.id,
              receivedItem.warehouseId || 1 // ใช้ warehouse default ถ้าไม่ระบุ
            );
          } catch (error) {
            if (error instanceof NotFoundException) {
              // สร้าง inventory ใหม่
              inventory = await this.inventoryService.create({
                productId: item.productId,
                branchId: purchaseOrder.branch.id,
                warehouseId: receivedItem.warehouseId || 1,
                quantity: 0
              });
            } else {
              throw error;
            }
          }

          // เพิ่มสินค้าเข้า inventory
          await this.inventoryService.createTransaction({
            inventoryId: inventory.id,
            type: TransactionType.PURCHASE,
            quantity: receivedItem.receivedQuantity,
            unitCost: item.unitPrice,
            description: `Purchase Order: ${purchaseOrder.poNumber} - ${item.productName}`,
            referenceType: 'purchase_order',
            referenceId: purchaseOrder.id,
            referenceNumber: purchaseOrder.poNumber
          }, userId);

        } catch (error) {
          console.error(`Error updating inventory for item ${item.productName}:`, error);
          // ไม่ throw error เพื่อไม่ให้กระทบกับการรับสินค้า
          // แต่ควรมี logging หรือ notification
        }
      }
    }

    // ตรวจสอบว่าได้รับสินค้าครบหรือไม่
    const updatedPo = await this.findOne(id);
    const allItemsReceived = updatedPo.items.every((item: any) => item.remainingQuantity === 0);

    if (allItemsReceived) {
      await this.purchaseOrderRepository.update(id, {
        status: PurchaseOrderStatus.RECEIVED,
        actualDeliveryDate: new Date()
      });
    }

    return this.findOne(id);
  }

  private async generatePoNumber(branchId: number): Promise<string> {
    const currentDate = new Date();
    const year = currentDate.getFullYear().toString().slice(-2);
    const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
    const branchCode = branchId.toString().padStart(2, '0');

    // หาเลขที่ PO ล่าสุดในเดือนนี้สำหรับสาขานี้
    const prefix = `PO${year}${month}${branchCode}`;
    const lastPo = await this.purchaseOrderRepository
      .createQueryBuilder('po')
      .where('po.poNumber LIKE :prefix', { prefix: `${prefix}%` })
      .orderBy('po.poNumber', 'DESC')
      .getOne();

    let sequence = 1;
    if (lastPo) {
      const lastSequence = parseInt(lastPo.poNumber.slice(-4));
      sequence = lastSequence + 1;
    }

    return `${prefix}${sequence.toString().padStart(4, '0')}`;
  }

  private getValidStatusTransitions(currentStatus: PurchaseOrderStatus): PurchaseOrderStatus[] {
    const transitions = {
      [PurchaseOrderStatus.DRAFT]: [PurchaseOrderStatus.PENDING, PurchaseOrderStatus.CANCELLED],
      [PurchaseOrderStatus.PENDING]: [PurchaseOrderStatus.APPROVED, PurchaseOrderStatus.CANCELLED],
      [PurchaseOrderStatus.APPROVED]: [PurchaseOrderStatus.ORDERED, PurchaseOrderStatus.CANCELLED],
      [PurchaseOrderStatus.ORDERED]: [PurchaseOrderStatus.RECEIVED, PurchaseOrderStatus.CANCELLED],
      [PurchaseOrderStatus.RECEIVED]: [PurchaseOrderStatus.COMPLETED],
      [PurchaseOrderStatus.COMPLETED]: [],
      [PurchaseOrderStatus.CANCELLED]: []
    };

    return transitions[currentStatus] || [];
  }
}
